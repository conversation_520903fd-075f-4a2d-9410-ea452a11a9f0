import "../styles/index.css";
import type { AppProps } from "next/app";
import { Inter } from "next/font/google";

// Configurar a fonte Inter com next/font
const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <div className="min-h-screen w-full body-bg relative">
      {/* Top Fade Grid Background */}
      <div className="absolute inset-0 z-0 bg-grid-pattern bg-fade-top" />
      <div className={`${inter.variable} font-sans relative z-10`}>
        <Component {...pageProps} />
      </div>
    </div>
  );
}

export default MyApp;
