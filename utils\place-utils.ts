import { PriceLevel, PriceLevelMap } from '../types/place';

/**
 * Format price level to human-readable string
 */
export function formatPriceLevel(priceLevel: number | undefined): string {
    if (priceLevel === undefined) return 'Not specified';

    const levels: PriceLevelMap = {
        0: 'Free',
        1: 'Inexpensive',
        2: 'Moderate',
        3: 'Expensive',
        4: 'Very Expensive',
    };

    return levels[priceLevel] || 'Not specified';
}

/**
 * Generate star rating array for display
 */
export function generateStarRating(rating: number): ('full' | 'half' | 'empty')[] {
    if (!rating) return Array(5).fill('empty');

    const stars: ('full' | 'half' | 'empty')[] = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
        if (i < fullStars) {
            stars.push('full');
        } else if (i === fullStars && hasHalfStar) {
            stars.push('half');
        } else {
            stars.push('empty');
        }
    }

    return stars;
}

/**
 * Format date from Unix timestamp
 */
export function formatReviewDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    });
}

/**
 * Get relative time string (e.g., "2 days ago")
 */
export function getRelativeTime(timestamp: number): string {
    const now = Date.now();
    const reviewTime = timestamp * 1000;
    const diffInSeconds = Math.floor((now - reviewTime) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 604800)} weeks ago`;
    if (diffInSeconds < ********) return `${Math.floor(diffInSeconds / 2592000)} months ago`;

    return `${Math.floor(diffInSeconds / ********)} years ago`;
}

/**
 * Extract primary type from types array
 */
export function getPrimaryType(types: string[]): string {
    if (!types || types.length === 0) return 'Place';

    // Priority order for display
    const priorityTypes = [
        'restaurant',
        'cafe',
        'bar',
        'hotel',
        'store',
        'museum',
        'park',
        'hospital',
        'school',
        'bank',
    ];

    const found = priorityTypes.find(type =>
        types.some(t => t.toLowerCase().includes(type))
    );

    if (found) {
        return found.charAt(0).toUpperCase() + found.slice(1);
    }

    // Return first type, formatted
    return types[0]
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
}

/**
 * Check if place is currently open based on opening hours
 */
export function isPlaceOpen(openingHours?: { open_now: boolean }): boolean | null {
    if (!openingHours) return null;
    return openingHours.open_now;
}

/**
 * Format phone number for display
 */
export function formatPhoneNumber(phone: string): string {
    // Remove all non-numeric characters
    const cleaned = phone.replace(/\D/g, '');

    // Format based on length
    if (cleaned.length === 10) {
        // US format: (*************
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }

    // Return original if not standard format
    return phone;
}

/**
 * Generate Google Maps directions URL
 */
export function getDirectionsUrl(placeId: string, placeName: string): string {
    const baseUrl = 'https://www.google.com/maps/dir/?api=1';
    const params = new URLSearchParams({
        destination: placeName,
        destination_place_id: placeId,
    });

    return `${baseUrl}&${params.toString()}`;
}

/**
 * Calculate aspect ratio for responsive images
 */
export function calculateAspectRatio(width: number, height: number): number {
    return height / width;
}

/**
 * Get optimized image dimensions
 */
export function getOptimizedImageDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number = 1600,
    maxHeight: number = 1600
): { width: number; height: number } {
    const width = Math.min(originalWidth, maxWidth);
    const height = Math.min(originalHeight, maxHeight);

    // Maintain aspect ratio
    const aspectRatio = originalHeight / originalWidth;

    if (width / height > originalWidth / originalHeight) {
        return {
            width: Math.round(height / aspectRatio),
            height,
        };
    } else {
        return {
            width,
            height: Math.round(width * aspectRatio),
        };
    }
}