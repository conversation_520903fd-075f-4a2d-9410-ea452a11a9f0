---
description: Repository Information Overview
alwaysApply: true
---

# Google Places API Information

## Summary

A Next.js application with TypeScript that uses the Google Places API to search for places, view detailed information, and display photos. The app provides a user-friendly interface for discovering places around the world with detailed information and high-quality photos.

## Structure

- **pages/**: Next.js pages including index, search, and place details (TypeScript)
- **pages/api/**: API endpoints for fetching additional photos and reviews
- **components/**: Reusable React components like layout, navigation, and search form
- **hooks/**: Custom React hooks for state management and API interactions
- **types/**: TypeScript type definitions and interfaces
- **styles/**: CSS styling with Tailwind CSS
- **.next/**: Next.js build output (generated)

## Language & Runtime

**Language**: TypeScript
**Version**: ES5 target with ESNext features
**Framework**: Next.js 15.4.4
**Package Manager**: npm/yarn/pnpm

## Dependencies

**Main Dependencies**:

- next: 15.4.4
- react: 19.1.0
- react-dom: 19.1.0
- @react-google-maps/api: 2.20.7
- form-data: ^4.0.4

**Development Dependencies**:

- typescript: ^5.8.3
- tailwindcss: ^4.1.11
- postcss: ^8.5.6
- @types/react: ^19.1.8
- @types/react-dom: ^19.1.6
- @types/node: ^24.1.0
- @types/google.maps: ^3.58.1
- postcss-preset-env: 10.2.4

## Build & Installation

```bash
# Install dependencies
npm install
# or
yarn
# or
pnpm install

# Development server
npm run dev
# or
yarn dev
# or
pnpm dev

# Production build
npm run build
# or
yarn build
# or
pnpm build

# Start production server
npm run start
# or
yarn start
# or
pnpm start
```

## Configuration

**Environment Variables**:

- `GPLACES_API`: Google Places API key (server-side)
- `NEXT_PUBLIC_API_KEY`: Google Places API key (client-side)
- `CHEVERETO_API_KEY`: API key for Chevereto image hosting

**TypeScript Config**:

- Target: ES5
- Module: ESNext
- Strict mode enabled
- JSX preservation
- Types: node, react, react-dom, google.maps

## API Endpoints

- **/api/more-photos**: Fetches additional photos for a place with pagination
- **/api/more-reviews**: Fetches additional reviews for a place
- **/api/upload-to-chevereto**: Uploads photos to Chevereto hosting service

## Custom Hooks

- **useReviews**: Manages reviews state, pagination, and retry logic
- **usePhotos**: Handles photo loading, pagination, and error states

## Main Pages

- **/**: Home page with search form
- **/search**: Search results page
- **/place/[id]**: Detailed place information page

## Features

- Google Places API integration
- Place search with autocomplete
- Detailed place information display
- Photo gallery with high-resolution options
- Reviews display with pagination
- Responsive design with Tailwind CSS
- TypeScript type safety throughout the application
