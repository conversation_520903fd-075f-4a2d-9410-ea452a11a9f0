import React, { memo } from "react";

interface LoadingSkeletonProps {
  type: "review" | "photo" | "text" | "custom";
  count?: number;
  className?: string;
  height?: string;
  width?: string;
}

/**
 * Reusable loading skeleton component for different content types
 */
const LoadingSkeleton: React.FC<LoadingSkeletonProps> = memo(
  ({ type, count = 1, className = "", height, width }) => {
    const renderSkeleton = () => {
      switch (type) {
        case "review":
          return (
            <div className="border-b border-gray-200 pb-4 animate-pulse">
              <div className="flex items-start space-x-4">
                <div className="h-10 w-10 bg-gray-200 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-3 bg-gray-200 rounded w-1/6" />
                  <div className="space-y-1">
                    <div className="h-3 bg-gray-200 rounded" />
                    <div className="h-3 bg-gray-200 rounded w-5/6" />
                    <div className="h-3 bg-gray-200 rounded w-4/6" />
                  </div>
                </div>
              </div>
            </div>
          );

        case "photo":
          return (
            <div className="w-full aspect-w-4 aspect-h-3 bg-gray-200 rounded-lg animate-pulse" />
          );

        case "text":
          return (
            <div className="space-y-2 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-full" />
              <div className="h-4 bg-gray-200 rounded w-5/6" />
            </div>
          );

        case "custom":
          return (
            <div
              className={`bg-gray-200 rounded animate-pulse ${className}`}
              style={{ height: height || "1rem", width: width || "100%" }}
            />
          );

        default:
          return null;
      }
    };

    return (
      <div className={className} aria-busy="true" aria-label="Loading content">
        {Array.from({ length: count }, (_, i) => (
          <div key={i} className={i < count - 1 ? "mb-4" : ""}>
            {renderSkeleton()}
          </div>
        ))}
      </div>
    );
  }
);

LoadingSkeleton.displayName = "LoadingSkeleton";

export default LoadingSkeleton;
