import React, { memo } from "react";
import { Review } from "../types/place";
import { formatReviewDate, getRelativeTime } from "../utils/place-utils";
import StarRating from "./StarRating";
import CopyButton from "./CopyButton";

interface ReviewItemProps {
  review: Review;
  onCopy: (text: string, field: string) => void;
}

/**
 * Individual review item component with author info, rating, and text
 */
const ReviewItem: React.FC<ReviewItemProps> = memo(({ review, onCopy }) => {
  const reviewDate = formatReviewDate(review.time);
  const relativeTime = getRelativeTime(review.time);

  return (
    <article
      className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0"
      aria-label={`Review by ${review.author_name}`}
    >
      <div className="flex items-start">
        <div className="shrink-0">
          {review.profile_photo_url ? (
            <img
              src={review.profile_photo_url}
              alt={`${review.author_name}'s profile`}
              className="h-10 w-10 rounded-full object-cover"
              loading="lazy"
            />
          ) : (
            <div
              className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center"
              aria-hidden="true"
            >
              <svg
                className="h-6 w-6 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
          )}
        </div>

        <div className="ml-4 flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {review.author_name}
              </h3>
              <div className="mt-1">
                <StarRating rating={review.rating} size="sm" />
              </div>
            </div>
            <div className="flex items-center">
              <time
                className="text-xs text-gray-500"
                dateTime={new Date(review.time * 1000).toISOString()}
                title={reviewDate}
              >
                {relativeTime}
              </time>
              <CopyButton
                text={`${review.author_name} - ${review.text}`}
                field={`Review by ${review.author_name}`}
                onCopy={onCopy}
                ariaLabel={`Copy review by ${review.author_name}`}
              />
            </div>
          </div>

          {review.text && (
            <div className="mt-2 text-sm text-gray-600 whitespace-pre-line">
              {review.text}
            </div>
          )}
        </div>
      </div>
    </article>
  );
});

ReviewItem.displayName = "ReviewItem";

export default ReviewItem;
