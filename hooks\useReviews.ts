import { useState, useCallback, useRef, useEffect } from 'react';
import { Review, ReviewsApiResponse } from '../types/place';

interface UseReviewsOptions {
    initialReviews: Review[];
    placeId: string;
}

interface UseReviewsReturn {
    reviews: Review[];
    isLoadingMore: boolean;
    error: string | null;
    noMoreReviews: boolean;
    loadMore: () => void;
    resetError: () => void;
}

/**
 * Custom hook for managing reviews state and pagination
 * Handles loading, error states, deduplication, and retry logic
 */
export function useReviews({ initialReviews, placeId }: UseReviewsOptions): UseReviewsReturn {
    const [reviews, setReviews] = useState<Review[]>(initialReviews || []);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [page, setPage] = useState(1);
    const [noMoreReviews, setNoMoreReviews] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);

    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const abortControllerRef = useRef<AbortController | null>(null);

    // Validate review data structure
    const validateReview = (review: Review): boolean => {
        return (
            review &&
            typeof review === 'object' &&
            typeof review.author_name === 'string' &&
            review.author_name.trim() !== '' &&
            typeof review.text === 'string' &&
            review.text.trim() !== '' &&
            typeof review.rating === 'number' &&
            review.rating >= 1 &&
            review.rating <= 5 &&
            typeof review.time === 'number' &&
            review.time > 0
        );
    };

    // Sort reviews chronologically (newest first)
    const sortReviewsChronologically = (reviewsArray: Review[]): Review[] => {
        return [...reviewsArray].sort((a, b) => b.time - a.time);
    };

    // Merge and deduplicate reviews
    const mergeReviews = (existingReviews: Review[], newReviews: Review[]): Review[] => {
        const validNewReviews = newReviews.filter(validateReview);
        const combined = [...existingReviews, ...validNewReviews];

        // Remove duplicates based on author_name, text, and time combination
        const unique = combined.filter(
            (review, index, self) =>
                index ===
                self.findIndex(
                    (r) =>
                        r.author_name === review.author_name &&
                        r.text === review.text &&
                        r.time === review.time
                )
        );

        return sortReviewsChronologically(unique);
    };

    // Ref to track current retry count for retry logic
    const retryCountRef = useRef(0);

    const loadMore = useCallback(async () => {
        if (isLoadingMore || noMoreReviews) return;

        // Clear any existing debounce timeout
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        // Cancel any existing request
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        // Debounce implementation
        debounceTimeoutRef.current = setTimeout(async () => {
            setIsLoadingMore(true);
            setError(null);

            // Create new AbortController for this request
            abortControllerRef.current = new AbortController();

            try {
                const response = await fetch(
                    `/api/more-reviews?placeId=${placeId}&page=${page + 1}&limit=5`,
                    {
                        signal: abortControllerRef.current.signal,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data: ReviewsApiResponse = await response.json();

                // Validate response structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid response format');
                }

                if (data.error) {
                    throw new Error(data.error);
                }

                if (data.reviews && Array.isArray(data.reviews)) {
                    if (data.reviews.length > 0) {
                        const mergedReviews = mergeReviews(reviews, data.reviews);
                        setReviews(mergedReviews);
                        setPage(page + 1);
                        // Reset retry count on success
                        setRetryCount(0);
                        retryCountRef.current = 0;

                        if (data.hasMore === false) {
                            setNoMoreReviews(true);
                        }
                    } else {
                        setNoMoreReviews(true);
                    }
                } else {
                    throw new Error('Invalid reviews data structure');
                }
            } catch (error: any) {
                // Check if request was aborted
                if (error instanceof Error && error.message.includes('aborted')) {
                    return; // Request was cancelled, don't show error
                }

                console.error('Error loading more reviews:', error);

                // Implement retry logic for network errors
                if (
                    retryCountRef.current < 3 &&
                    (error.message?.includes('fetch') ||
                        error.message?.includes('network'))
                ) {
                    // Update both state and ref
                    setRetryCount(prev => {
                        const nextCount = prev + 1;
                        retryCountRef.current = nextCount;
                        return nextCount;
                    });

                    // Use the updated retry count from the ref for backoff calculation
                    setTimeout(() => {
                        loadMore();
                    }, 1000 * Math.pow(2, retryCountRef.current)); // Exponential backoff
                } else {
                    setError(
                        error.message ||
                        'Error loading more reviews. Please try again.'
                    );
                }
            } finally {
                setIsLoadingMore(false);
                abortControllerRef.current = null;
            }
        }, 300); // 300ms debounce delay
    }, [isLoadingMore, noMoreReviews, placeId, page]);

    const resetError = useCallback(() => {
        setError(null);
        setRetryCount(0);
    }, []);

    // Initialize reviews with proper sorting
    useEffect(() => {
        if (initialReviews && initialReviews.length > 0) {
            const validReviews = initialReviews.filter(validateReview);
            const sortedReviews = sortReviewsChronologically(validReviews);
            setReviews(sortedReviews);
        }
    }, [initialReviews]);

    // Cleanup effect
    useEffect(() => {
        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    return {
        reviews,
        isLoadingMore,
        error,
        noMoreReviews,
        loadMore,
        resetError,
    };
}
