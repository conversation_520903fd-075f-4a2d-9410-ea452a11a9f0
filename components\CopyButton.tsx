import React, { memo } from "react";

interface CopyButtonProps {
  text: string;
  field: string;
  onCopy: (text: string, field: string) => void;
  className?: string;
  ariaLabel?: string;
}

/**
 * Reusable copy button component with accessibility support
 */
const CopyButton: React.FC<CopyButtonProps> = memo(
  ({ text, field, onCopy, className = "", ariaLabel }) => {
    if (!text) return null;

    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onCopy(text, field);
    };

    return (
      <button
        onClick={handleClick}
        className={`ml-2 inline-flex items-center justify-center p-2 bg-white border border-gray-300 rounded-lg text-gray-600 shadow-none hover:bg-gray-50 hover:border-gray-400 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] ${className}`}
        title={`Copy ${field}`}
        aria-label={ariaLabel || `Copy ${field} to clipboard`}
        type="button"
      >
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
          />
        </svg>
      </button>
    );
  }
);

CopyButton.displayName = "CopyButton";

export default CopyButton;
