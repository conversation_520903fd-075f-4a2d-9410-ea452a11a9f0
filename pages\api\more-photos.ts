/**
 * API endpoint para carregar mais fotos de um lugar usando Google Places API
 *
 * ESTRATÉGIA:
 * - A API do Google Places retorna todas as referências de fotos de uma vez
 * - Este endpoint implementa paginação no lado servidor para melhor performance
 * - Gera URLs das fotos sob demanda para economizar recursos
 * - Mantém a chave da API segura no servidor
 */

import { NextApiRequest, NextApiResponse } from 'next';

interface PhotoReference {
  photo_reference: string;
  width: number;
  height: number;
  html_attributions: string[];
}

// Cache para armazenar referências de fotos já obtidas
const photosCache = new Map<string, PhotoReference[]>();

/**
 * Busca todas as referências de fotos da API do Google Places com retry logic
 */
async function fetchAllPhotoReferences(placeId: string): Promise<PhotoReference[]> {
  const cacheKey = `${placeId}_photo_refs`;

  // Verificar cache primeiro
  if (photosCache.has(cacheKey)) {
    return photosCache.get(cacheKey) as PhotoReference[];
  }

  const maxRetries = 3;
  let currentDelay = 100; // Start with 100ms delay
  const maxDelay = 5000; // Maximum 5 seconds delay

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const googleApiUrl = `https://maps.googleapis.com/maps/api/place/details/json?key=${process.env.GPLACES_API}&place_id=${placeId}&fields=photos`;

      const response = await fetch(googleApiUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === 'OK' && data.result && data.result.photos) {
        const photoReferences = data.result.photos.map((photo: any) => ({
          photo_reference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
          html_attributions: photo.html_attributions || []
        }));

        // Armazenar no cache por 30 minutos (photo references podem expirar)
        photosCache.set(cacheKey, photoReferences);
        setTimeout(() => photosCache.delete(cacheKey), 30 * 60 * 1000);

        return photoReferences;
      } else if (data.status !== 'UNKNOWN_ERROR') {
        // Erros que não podem ser resolvidos com retry
        console.error('API error that cannot be retried:', data.status);
        return [];
      }

      throw new Error(`API returned status: ${data.status}`);
    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed:`, error);

      if (attempt === maxRetries) {
        console.error('Max retries reached for photo references');
        return [];
      }

      // Exponential backoff with jitter
      const jitter = Math.random() * 0.1 * currentDelay;
      const delayWithJitter = currentDelay + jitter;

      console.log(`Waiting ${delayWithJitter}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delayWithJitter));

      currentDelay = Math.min(currentDelay * 2, maxDelay);
    }
  }

  return [];
}

/**
 * Gera URLs das fotos com a chave da API otimizadas para máxima qualidade
 */
function generatePhotoUrls(photoReferences: PhotoReference[], startIndex: number, limit: number) {
  const batch = photoReferences.slice(startIndex, startIndex + limit);

  return batch.map(photo => {
    // Usar o máximo permitido pela API (1600px) ou a dimensão original, o que for menor
    const maxWidth = Math.min(photo.width, 1600);
    const maxHeight = Math.min(photo.height, 1600);

    return {
      thumbnail: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.GPLACES_API}&maxwidth=400&photoreference=${photo.photo_reference}`,
      large: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.GPLACES_API}&maxwidth=${maxWidth}&maxheight=${maxHeight}&photoreference=${photo.photo_reference}`,
      attributions: photo.html_attributions,
      width: photo.width,
      height: photo.height
    };
  });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Apenas aceitar requisições GET
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: 'Método não permitido',
      message: 'Este endpoint aceita apenas requisições GET'
    });
  }

  const { placeId, page = '1', limit = '6' } = req.query;

  // Validar parâmetros obrigatórios
  if (!placeId || typeof placeId !== 'string') {
    return res.status(400).json({
      error: 'Parâmetro obrigatório',
      message: 'placeId é obrigatório e deve ser uma string'
    });
  }

  // Extrair valores como string (req.query pode retornar arrays)
  const pageStr = Array.isArray(page) ? page[0] : page;
  const limitStr = Array.isArray(limit) ? limit[0] : limit;

  // Validar e converter parâmetros numéricos
  const pageNum = parseInt(pageStr as string, 10);
  const limitNum = parseInt(limitStr as string, 10);

  if (isNaN(pageNum) || pageNum < 1) {
    return res.status(400).json({
      error: 'Parâmetro inválido',
      message: 'page deve ser um número inteiro maior que 0'
    });
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 20) {
    return res.status(400).json({
      error: 'Parâmetro inválido',
      message: 'limit deve ser um número entre 1 e 20'
    });
  }

  // Validar se a chave da API está configurada
  if (!process.env.GPLACES_API) {
    console.error('GPLACES_API não está configurada no ambiente');
    return res.status(500).json({
      error: 'Configuração do servidor',
      message: 'Chave da API não está configurada'
    });
  }

  try {
    console.log(`Buscando fotos para placeId: ${placeId}, página: ${pageNum}, limite: ${limitNum}`);

    // Buscar todas as referências de fotos
    const allPhotoReferences = await fetchAllPhotoReferences(placeId as string);

    if (allPhotoReferences.length === 0) {
      return res.status(200).json({
        photos: [],
        hasMore: false,
        page: pageNum,
        limit: limitNum,
        total: 0,
        message: 'Nenhuma foto disponível para este local.'
      });
    }

    // Calcular índices para paginação
    // O SSR já carrega as primeiras 10 fotos, então começamos a partir da 11ª foto
    const initialPhotosLoaded = 10;
    const startIndex = initialPhotosLoaded + (pageNum - 1) * limitNum;
    const hasMore = startIndex + limitNum < allPhotoReferences.length;

    // Verificar se a página solicitada existe
    if (startIndex >= allPhotoReferences.length) {
      return res.status(200).json({
        photos: [],
        hasMore: false,
        page: pageNum,
        limit: limitNum,
        total: 0,
        message: 'Não há mais fotos disponíveis.'
      });
    }

    // Gerar URLs para o lote de fotos solicitado
    const photos = generatePhotoUrls(allPhotoReferences, startIndex, limitNum);

    console.log(`Retornando ${photos.length} fotos, hasMore: ${hasMore}`);

    // Simular atraso de rede realístico
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    return res.status(200).json({
      photos: photos,
      hasMore: hasMore,
      page: pageNum,
      limit: limitNum,
      total: photos.length,
      totalAvailable: allPhotoReferences.length,
      message: `${photos.length} fotos carregadas de ${allPhotoReferences.length} disponíveis.`
    });

  } catch (error) {
    console.error('Erro ao buscar mais fotos:', error);

    // Tratar diferentes tipos de erro
    if (error instanceof Error &&
      (error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT'))) {
      return res.status(503).json({
        error: 'Erro de conectividade',
        message: 'Não foi possível conectar à API do Google Places. Tente novamente em alguns instantes.'
      });
    }

    return res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro inesperado ao buscar fotos. Tente novamente.'
    });
  }
}
