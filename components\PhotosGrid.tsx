import React, { memo } from "react";
import { Photo } from "../types/place";
import PlacePhoto from "./placePhoto";
import LoadingButton from "./LoadingButton";
import ErrorMessage from "./ErrorMessage";
import LoadingSkeleton from "./LoadingSkeleton";

interface PhotosGridProps {
  photos: Photo[];
  totalPhotosAvailable: number;
  isLoadingMore: boolean;
  error: string | null;
  noMorePhotos: boolean;
  onLoadMore: () => void;
  onResetError: () => void;
}

/**
 * Photos grid component with lazy loading and error handling
 */
const PhotosGrid: React.FC<PhotosGridProps> = memo(
  ({
    photos,
    totalPhotosAvailable,
    isLoadingMore,
    error,
    noMorePhotos,
    onLoadMore,
    onResetError,
  }) => {
    return (
      <section aria-labelledby="photos-heading">
        <h2
          id="photos-heading"
          className="text-xl font-bold text-gray-900 mb-4"
        >
          Photos
          {totalPhotosAvailable > 0 && (
            <span className="text-sm font-normal text-gray-500 ml-2">
              ({photos.length} of {totalPhotosAvailable})
            </span>
          )}
        </h2>

        {photos.length > 0 ? (
          <div className="space-y-6">
            <div className="flex -mx-2 flex-wrap" role="list">
              {photos.map((photo, index) => (
                <div
                  key={`photo-${index}`}
                  className="w-6/12 md:w-4/12 mb-4 px-2"
                  role="listitem"
                >
                  <PlacePhoto photo={photo} />
                </div>
              ))}
            </div>

            {/* Error message */}
            {error && (
              <ErrorMessage
                error={error}
                onRetry={() => {
                  onResetError();
                  onLoadMore();
                }}
              />
            )}

            {/* Load more button */}
            {!noMorePhotos && !error && (
              <div className="text-center">
                <LoadingButton
                  onClick={onLoadMore}
                  isLoading={isLoadingMore}
                  loadingText="Loading photos..."
                  variant="primary"
                  size="md"
                  ariaLabel="Load more photos"
                >
                  Load more photos
                </LoadingButton>
              </div>
            )}

            {/* No more photos message */}
            {noMorePhotos && photos.length < totalPhotosAvailable && (
              <div className="text-center text-gray-500 text-sm">
                All photos have been loaded.
              </div>
            )}

            {/* Loading skeleton */}
            {isLoadingMore && (
              <div className="flex -mx-2 flex-wrap">
                {Array.from({ length: 6 }, (_, i) => (
                  <div
                    key={`skeleton-${i}`}
                    className="w-6/12 md:w-4/12 mb-4 px-2"
                  >
                    <div className="card overflow-hidden">
                      <LoadingSkeleton type="photo" />
                      <div className="p-4">
                        <LoadingSkeleton
                          type="custom"
                          height="40px"
                          className="mb-3"
                        />
                        <LoadingSkeleton
                          type="custom"
                          height="40px"
                          className="mb-3"
                        />
                        <LoadingSkeleton type="custom" height="40px" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="mt-2">No photos available for this place.</p>
          </div>
        )}
      </section>
    );
  }
);

PhotosGrid.displayName = "PhotosGrid";

export default PhotosGrid;
