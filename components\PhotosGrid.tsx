import React, { memo, useState, useCallback } from "react";
import { Photo, BatchUploadState, BatchUploadResponse } from "../types/place";
import PlacePhoto from "./placePhoto";
import LoadingButton from "./LoadingButton";
import ErrorMessage from "./ErrorMessage";
import LoadingSkeleton from "./LoadingSkeleton";

interface PhotosGridProps {
  photos: Photo[];
  totalPhotosAvailable: number;
  isLoadingMore: boolean;
  error: string | null;
  noMorePhotos: boolean;
  onLoadMore: () => void;
  onResetError: () => void;
}

/**
 * Photos grid component with lazy loading, error handling, and batch upload functionality
 */
const PhotosGrid: React.FC<PhotosGridProps> = memo(
  ({
    photos,
    totalPhotosAvailable,
    isLoadingMore,
    error,
    noMorePhotos,
    onLoadMore,
    onResetError,
  }) => {
    // Batch upload state
    const [batchUploadState, setBatchUploadState] = useState<BatchUploadState>({
      isUploading: false,
      progress: { current: 0, total: 0 },
      results: null,
      selectedPhotos: new Set<number>(),
    });

    // Toggle photo selection
    const togglePhotoSelection = useCallback((index: number) => {
      setBatchUploadState((prev) => {
        const newSelected = new Set(prev.selectedPhotos);
        if (newSelected.has(index)) {
          newSelected.delete(index);
        } else {
          newSelected.add(index);
        }
        return { ...prev, selectedPhotos: newSelected };
      });
    }, []);

    // Select all photos
    const selectAllPhotos = useCallback(() => {
      setBatchUploadState((prev) => ({
        ...prev,
        selectedPhotos: new Set(photos.map((_, index) => index)),
      }));
    }, [photos]);

    // Deselect all photos
    const deselectAllPhotos = useCallback(() => {
      setBatchUploadState((prev) => ({
        ...prev,
        selectedPhotos: new Set<number>(),
      }));
    }, []);

    // Perform batch upload
    const performBatchUpload = useCallback(async () => {
      const selectedIndices = Array.from(batchUploadState.selectedPhotos);
      if (selectedIndices.length === 0) return;

      const selectedImageUrls = selectedIndices.map(
        (index) => photos[index].large
      );

      setBatchUploadState((prev) => ({
        ...prev,
        isUploading: true,
        progress: { current: 0, total: selectedIndices.length },
        results: null,
      }));

      try {
        const response = await fetch("/api/upload-to-chevereto", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            imageUrls: selectedImageUrls,
          }),
        });

        const data: BatchUploadResponse = await response.json();

        setBatchUploadState((prev) => ({
          ...prev,
          isUploading: false,
          results: data,
          selectedPhotos: new Set<number>(), // Clear selection after upload
        }));
      } catch (error) {
        console.error("Erro no batch upload:", error);
        setBatchUploadState((prev) => ({
          ...prev,
          isUploading: false,
          results: {
            success: false,
            message: "Erro na comunicação com o servidor",
            data: {
              successful: [],
              failed: selectedImageUrls.map((url) => ({
                originalUrl: url,
                error: "Network Error",
                message: "Erro na comunicação com o servidor",
              })),
              summary: {
                total: selectedImageUrls.length,
                successful: 0,
                failed: selectedImageUrls.length,
              },
            },
          },
        }));
      }
    }, [batchUploadState.selectedPhotos, photos]);

    // Clear batch upload results
    const clearBatchResults = useCallback(() => {
      setBatchUploadState((prev) => ({
        ...prev,
        results: null,
      }));
    }, []);
    return (
      <section aria-labelledby="photos-heading">
        <h2
          id="photos-heading"
          className="text-xl font-bold text-gray-900 mb-4"
        >
          Photos
          {totalPhotosAvailable > 0 && (
            <span className="text-sm font-normal text-gray-500 ml-2">
              ({photos.length} of {totalPhotosAvailable})
            </span>
          )}
        </h2>

        {/* Batch Upload Controls */}
        {photos.length > 0 && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Batch Upload to Chevereto
            </h3>

            <div className="flex flex-wrap items-center gap-3 mb-4">
              <button
                onClick={selectAllPhotos}
                disabled={batchUploadState.isUploading}
                className="btn-secondary text-sm"
              >
                Select All ({photos.length})
              </button>

              <button
                onClick={deselectAllPhotos}
                disabled={batchUploadState.isUploading}
                className="btn-secondary text-sm"
              >
                Deselect All
              </button>

              {batchUploadState.selectedPhotos.size > 0 && (
                <span className="text-sm text-gray-600">
                  {batchUploadState.selectedPhotos.size} selected
                </span>
              )}
            </div>

            {batchUploadState.selectedPhotos.size > 0 && (
              <div className="mb-4">
                <LoadingButton
                  onClick={performBatchUpload}
                  isLoading={batchUploadState.isUploading}
                  loadingText={`Uploading ${batchUploadState.progress.current} of ${batchUploadState.progress.total}...`}
                  variant="primary"
                  size="md"
                  ariaLabel="Upload selected photos to Chevereto"
                  disabled={batchUploadState.selectedPhotos.size === 0}
                >
                  Upload {batchUploadState.selectedPhotos.size} Selected Photo
                  {batchUploadState.selectedPhotos.size !== 1 ? "s" : ""} to
                  Chevereto
                </LoadingButton>
              </div>
            )}

            {/* Batch Upload Results */}
            {batchUploadState.results && (
              <div className="mt-4 p-4 border rounded-md bg-white">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">
                    Upload Results
                  </h4>
                  <button
                    onClick={clearBatchResults}
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    ✕ Close
                  </button>
                </div>

                <div className="mb-3">
                  <div
                    className={`text-sm font-medium ${
                      batchUploadState.results.success
                        ? "text-green-800"
                        : "text-red-800"
                    }`}
                  >
                    {batchUploadState.results.message}
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    Total: {batchUploadState.results.data.summary.total} |
                    Successful:{" "}
                    {batchUploadState.results.data.summary.successful} | Failed:{" "}
                    {batchUploadState.results.data.summary.failed}
                  </div>
                </div>

                {/* Successful uploads */}
                {batchUploadState.results.data.successful.length > 0 && (
                  <div className="mb-3">
                    <div className="text-sm font-medium text-green-800 mb-2">
                      ✓ Successfully uploaded (
                      {batchUploadState.results.data.successful.length}):
                    </div>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {batchUploadState.results.data.successful.map(
                        (result, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-2 text-xs"
                          >
                            <input
                              type="text"
                              value={result.uploadedUrl}
                              readOnly
                              className="flex-1 bg-green-50 border border-green-200 rounded px-2 py-1 text-green-700 font-mono"
                              onClick={(e) =>
                                (e.target as HTMLInputElement).select()
                              }
                            />
                            <button
                              onClick={() =>
                                navigator.clipboard.writeText(
                                  result.uploadedUrl
                                )
                              }
                              className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                              title="Copy URL"
                            >
                              Copy
                            </button>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}

                {/* Failed uploads */}
                {batchUploadState.results.data.failed.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-red-800 mb-2">
                      ✗ Failed uploads (
                      {batchUploadState.results.data.failed.length}):
                    </div>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {batchUploadState.results.data.failed.map(
                        (error, index) => (
                          <div
                            key={index}
                            className="text-xs text-red-600 bg-red-50 border border-red-200 rounded px-2 py-1"
                          >
                            <div className="font-medium">
                              Error: {error.error}
                            </div>
                            <div>{error.message}</div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {photos.length > 0 ? (
          <div className="space-y-6">
            <div className="flex -mx-2 flex-wrap" role="list">
              {photos.map((photo, index) => (
                <div
                  key={`photo-${index}`}
                  className="w-6/12 md:w-4/12 mb-4 px-2"
                  role="listitem"
                >
                  <PlacePhoto
                    photo={photo}
                    isSelectable={true}
                    isSelected={batchUploadState.selectedPhotos.has(index)}
                    onSelectionChange={() => togglePhotoSelection(index)}
                    isBatchUploading={batchUploadState.isUploading}
                  />
                </div>
              ))}
            </div>

            {/* Error message */}
            {error && (
              <ErrorMessage
                error={error}
                onRetry={() => {
                  onResetError();
                  onLoadMore();
                }}
              />
            )}

            {/* Load more button */}
            {!noMorePhotos && !error && (
              <div className="text-center">
                <LoadingButton
                  onClick={onLoadMore}
                  isLoading={isLoadingMore}
                  loadingText="Loading photos..."
                  variant="primary"
                  size="md"
                  ariaLabel="Load more photos"
                >
                  Load more photos
                </LoadingButton>
              </div>
            )}

            {/* No more photos message */}
            {noMorePhotos && photos.length < totalPhotosAvailable && (
              <div className="text-center text-gray-500 text-sm">
                All photos have been loaded.
              </div>
            )}

            {/* Loading skeleton */}
            {isLoadingMore && (
              <div className="flex -mx-2 flex-wrap">
                {Array.from({ length: 6 }, (_, i) => (
                  <div
                    key={`skeleton-${i}`}
                    className="w-6/12 md:w-4/12 mb-4 px-2"
                  >
                    <div className="card overflow-hidden">
                      <LoadingSkeleton type="photo" />
                      <div className="p-4">
                        <LoadingSkeleton
                          type="custom"
                          height="40px"
                          className="mb-3"
                        />
                        <LoadingSkeleton
                          type="custom"
                          height="40px"
                          className="mb-3"
                        />
                        <LoadingSkeleton type="custom" height="40px" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="mt-2">No photos available for this place.</p>
          </div>
        )}
      </section>
    );
  }
);

PhotosGrid.displayName = "PhotosGrid";

export default PhotosGrid;
