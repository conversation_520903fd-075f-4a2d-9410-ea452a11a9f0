import Head from "next/head";
import SearchForm from "../components/searchForm";
import Layout from "../components/layout";

export default function HomePage() {
  return (
    <Layout>
      <Head>
        <title>GPlaces API</title>
        <meta
          name="description"
          content="Discover amazing places around the world with detailed information and photos."
        />
      </Head>

      <div className="min-h-screen py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="mb-8">
              <span className="text-6xl">🌍</span>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              GPlaces API
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover incredible places, explore detailed photos, and get
              comprehensive information about any location worldwide.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <SearchForm />
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">🔍</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Smart Search
              </h3>
              <p className="text-gray-600">
                Use Google Autocomplete to find any place quickly.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">📸</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                High-Quality Photos
              </h3>
              <p className="text-gray-600">
                View real photos of places with high-resolution download
                options.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">ℹ️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Detailed Information
              </h3>
              <p className="text-gray-600">
                Get full addresses, place types, and much more.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
