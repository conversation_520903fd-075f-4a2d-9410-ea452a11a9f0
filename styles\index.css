@import 'tailwindcss';
@import './animations.css';

@theme {
  --font-sans: var(--font-inter), system-ui, sans-serif;
}

/* Background grid pattern with fade effect */
.body-bg {
  background-color: #f8fafc;
}

.bg-grid-pattern {
  background-image:
    linear-gradient(to right, #e2e8f0 1px, transparent 1px),
    linear-gradient(to bottom, #e2e8f0 1px, transparent 1px);
  background-size: 20px 30px;
}

.bg-fade-top {
  -webkit-mask-image: radial-gradient(ellipse 70% 60% at 50% 0%, #000 60%, transparent 100%);
  mask-image: radial-gradient(ellipse 70% 60% at 50% 0%, #000 60%, transparent 100%);
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

body {
  font-family: var(--font-inter), system-ui, sans-serif;
}

.btn-primary {
  @apply inline-flex items-center justify-center px-6 py-3 bg-black border border-transparent rounded-lg font-semibold text-sm text-white uppercase tracking-wider hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-6 py-3 bg-white border border-gray-300 rounded-lg font-semibold text-sm text-gray-700 uppercase tracking-wider shadow-sm hover:bg-gray-50 hover:border-gray-400 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black active:bg-gray-100 active:border-gray-500 disabled:opacity-50 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98];
}

.form-input {
  @apply shadow-xs focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md;
}

.card {
  @apply bg-white overflow-hidden shadow-sm rounded-2xl;
}