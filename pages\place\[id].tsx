import React from "react";
import { GetServerSideProps } from "next";
import Link from "next/link";
import dynamic from "next/dynamic";

// Components
import Layout from "../../components/layout";
import PlaceItem from "../../components/placeItem";
import PlaceInfo from "../../components/PlaceInfo";
import ReviewsSection from "../../components/ReviewsSection";
import PhotosGrid from "../../components/PhotosGrid";
import CopySuccessNotification from "../../components/CopySuccessNotification";
import PlaceSEO from "../../components/PlaceSEO";
import ErrorBoundary from "../../components/ErrorBoundary";
import PlaceSummaryButton from "../../components/PlaceSummaryButton";

// Hooks
import { useReviews } from "../../hooks/useReviews";
import { usePhotos } from "../../hooks/usePhotos";
import { useClipboard } from "../../hooks/useClipboard";

// Types
import { PlaceDetailsProps, Photo } from "../../types/place";

// Utils
import { getOptimizedImageDimensions } from "../../utils/place-utils";

// Lazy load heavy components for better performance
const LazyReviewsSection = dynamic(
  () => import("../../components/ReviewsSection"),
  {
    loading: () => (
      <div className="animate-pulse h-64 bg-gray-100 rounded-lg" />
    ),
    ssr: true,
  }
);

const LazyPhotosGrid = dynamic(() => import("../../components/PhotosGrid"), {
  loading: () => <div className="animate-pulse h-64 bg-gray-100 rounded-lg" />,
  ssr: true,
});

/**
 * Server-side props fetching for place details
 * Fetches place information and initial photos from Google Places API
 */
export const getServerSideProps: GetServerSideProps<PlaceDetailsProps> = async (
  context
) => {
  const { id } = context.params || {};

  if (!id || typeof id !== "string") {
    return {
      notFound: true,
    };
  }

  try {
    const fields = [
      "formatted_address",
      "icon",
      "name",
      "photos",
      "place_id",
      "types",
      "rating",
      "reviews",
      "formatted_phone_number",
      "website",
      "opening_hours",
      "price_level",
      "international_phone_number",
      "vicinity",
      "url",
    ].join(",");

    const url = `https://maps.googleapis.com/maps/api/place/details/json?key=${process.env.GPLACES_API}&place_id=${id}&fields=${fields}`;
    const res = await fetch(url);

    if (!res.ok) {
      throw new Error(`API request failed with status ${res.status}`);
    }

    const resJson = await res.json();

    if (resJson.status !== "OK") {
      console.error(
        "Google Places API error:",
        resJson.status,
        resJson.error_message
      );
      return {
        notFound: true,
      };
    }

    let photos: Photo[] = [];
    let totalPhotosAvailable = 0;

    if (resJson.result?.photos) {
      totalPhotosAvailable = resJson.result.photos.length;

      // Load only the first 10 photos initially for performance
      const initialPhotosLimit = 10;
      const initialPhotos = resJson.result.photos.slice(0, initialPhotosLimit);

      photos = initialPhotos.map((photo: any) => {
        const { width, height } = getOptimizedImageDimensions(
          photo.width,
          photo.height
        );

        return {
          thumbnail: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.GPLACES_API}&maxwidth=400&photoreference=${photo.photo_reference}`,
          large: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.GPLACES_API}&maxwidth=${width}&maxheight=${height}&photoreference=${photo.photo_reference}`,
          attributions: photo.html_attributions || [],
          width: photo.width,
          height: photo.height,
        };
      });
    }

    return {
      props: {
        data: {
          status: resJson.status,
          result: resJson.result,
          photos,
          totalPhotosAvailable,
        },
      },
    };
  } catch (error) {
    console.error("Error fetching place details:", error);
    return {
      notFound: true,
    };
  }
};

/**
 * Place Details Page Component
 * Displays comprehensive information about a specific place including
 * details, reviews, photos with optimized loading and error handling
 */
export default function PlaceDetails({ data }: PlaceDetailsProps) {
  // Custom hooks for state management
  const { copySuccess, copyToClipboard } = useClipboard();

  const {
    reviews,
    isLoadingMore: isLoadingMoreReviews,
    error: reviewsError,
    noMoreReviews,
    loadMore: loadMoreReviews,
    resetError: resetReviewsError,
  } = useReviews({
    initialReviews: data.result.reviews || [],
    placeId: data.result.place_id,
  });

  const {
    photos,
    isLoadingMore: isLoadingMorePhotos,
    error: photosError,
    noMorePhotos,
    loadMore: loadMorePhotos,
    resetError: resetPhotosError,
  } = usePhotos({
    initialPhotos: data.photos || [],
    totalPhotosAvailable: data.totalPhotosAvailable,
    placeId: data.result.place_id,
  });

  if (data.status !== "OK") {
    return (
      <Layout>
        <div className="container mx-auto px-4 md:px-10">
          <div className="my-10 md:my-20 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Place Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              Sorry, we couldn't find the place you're looking for.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 rounded-md border border-indigo-600 text-sm font-medium text-indigo-600 hover:text-white hover:bg-indigo-600 transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ErrorBoundary>
      <Layout>
        <PlaceSEO place={data.result} photos={photos} />

        <div className="container mx-auto px-4 md:px-10">
          <div className="my-10 md:my-20 w-full lg:w-8/12 mx-auto">
            {/* Back button */}
            <nav aria-label="Breadcrumb">
              <Link
                href="/"
                className="inline-flex items-center px-4 py-2 rounded-md border border-indigo-600 text-sm font-medium text-indigo-600 hover:text-white hover:bg-indigo-600 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Back
              </Link>
            </nav>

            <hr className="my-10" />

            {/* Place summary */}
            <PlaceItem place={data.result} noButton onCopy={copyToClipboard} />

            {/* Copy success notification */}
            <CopySuccessNotification message={copySuccess} />

            {/* Place summary copy button */}
            <div className="mt-4 mb-6">
              <PlaceSummaryButton
                place={data.result}
                reviews={reviews}
                onCopy={copyToClipboard}
              />
            </div>

            {/* Place information */}
            <PlaceInfo place={data.result} onCopy={copyToClipboard} />

            {/* Reviews section */}
            {reviews && reviews.length > 0 && (
              <LazyReviewsSection
                reviews={reviews}
                isLoadingMore={isLoadingMoreReviews}
                error={reviewsError}
                noMoreReviews={noMoreReviews}
                onLoadMore={loadMoreReviews}
                onResetError={resetReviewsError}
                onCopy={copyToClipboard}
              />
            )}

            <hr className="my-10" />

            {/* Photos section */}
            <LazyPhotosGrid
              photos={photos}
              totalPhotosAvailable={data.totalPhotosAvailable}
              isLoadingMore={isLoadingMorePhotos}
              error={photosError}
              noMorePhotos={noMorePhotos}
              onLoadMore={loadMorePhotos}
              onResetError={resetPhotosError}
            />
          </div>
        </div>
      </Layout>
    </ErrorBoundary>
  );
}
