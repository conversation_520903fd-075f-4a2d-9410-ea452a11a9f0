import React from "react";
import Link from "next/link";
import CopyButton from "./CopyButton";

interface Place {
  name: string;
  formatted_address: string;
  place_id: string;
  types: string[];
  image?: string;
}

interface PlaceItemProps {
  place: Place;
  noButton?: boolean;
  onCopy?: (text: string, field: string) => void;
}

export default function PlaceItem({ place, noButton, onCopy }: PlaceItemProps) {
  return (
    <div className="card mb-6 overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <div className="flex flex-col sm:flex-row">
        <div className="flex-1 p-6 flex flex-col justify-between">
          <div>
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-2xl font-bold text-gray-900 leading-tight">
                {place.name}
              </h3>
            </div>

            <div className="flex items-start text-gray-600 mb-4">
              <svg
                className="w-4 h-4 mt-1 mr-2 shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              <span className="text-sm leading-relaxed flex-1">
                {place.formatted_address}
              </span>
              {onCopy && (
                <CopyButton
                  text={place.formatted_address}
                  field="Address"
                  onCopy={onCopy}
                  ariaLabel="Copy place address"
                />
              )}
            </div>

            {place.types && place.types.length > 0 && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {place.types.slice(0, 4).map((type, i) => (
                    <span
                      key={i}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                    >
                      {type
                        .replace(/_/g, " ")
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </span>
                  ))}
                  {place.types.length > 4 && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                      +{place.types.length - 4} mais
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {!noButton && (
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <Link
                href={`/place/${place.place_id}`}
                className="btn-primary inline-flex items-center"
              >
                Ver Detalhes
                <svg
                  className="ml-2 w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
