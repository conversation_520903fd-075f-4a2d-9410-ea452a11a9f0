import React, { memo, useState, useEffect } from "react";

interface CopySuccessNotificationProps {
  message: string;
  duration?: number;
}

/**
 * Copy success notification component with auto-dismiss
 */
const CopySuccessNotification: React.FC<CopySuccessNotificationProps> = memo(
  ({ message, duration = 2000 }) => {
    const [isVisible, setIsVisible] = useState(!!message);

    useEffect(() => {
      if (!message) {
        setIsVisible(false);
        return;
      }

      setIsVisible(true);
      const timer = setTimeout(() => setIsVisible(false), duration);
      return () => clearTimeout(timer);
    }, [message, duration]);

    if (!isVisible) return null;

    return (
      <div
        className="fixed top-4 right-4 z-50 animate-slide-in-right"
        role="status"
        aria-live="polite"
      >
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded-md shadow-lg flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{message}</span>
        </div>
      </div>
    );
  }
);

CopySuccessNotification.displayName = "CopySuccessNotification";

export default CopySuccessNotification;
