import { useState, useCallback, useRef, useEffect } from 'react';
import { Photo, PhotosApiResponse } from '../types/place';

interface UsePhotosOptions {
    initialPhotos: Photo[];
    totalPhotosAvailable: number;
    placeId: string;
}

interface UsePhotosReturn {
    photos: Photo[];
    isLoadingMore: boolean;
    error: string | null;
    noMorePhotos: boolean;
    loadMore: () => void;
    resetError: () => void;
    currentCount: number;
    totalCount: number;
}

/**
 * Custom hook for managing photos state and pagination
 * Handles loading, error states, and retry logic with exponential backoff
 */
export function usePhotos({
    initialPhotos,
    totalPhotosAvailable,
    placeId
}: UsePhotosOptions): UsePhotosReturn {
    const [photos, setPhotos] = useState<Photo[]>(initialPhotos || []);
    const [page, setPage] = useState(1); // Page 1 already loaded in SSR
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [noMorePhotos, setNoMorePhotos] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const abortControllerRef = useRef<AbortController | null>(null);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Initialize noMorePhotos state based on initial data
    useEffect(() => {
        if (totalPhotosAvailable > 0 && initialPhotos) {
            // If there are 10 or fewer photos total, no more to load
            setNoMorePhotos(totalPhotosAvailable <= 10);
        }
    }, [totalPhotosAvailable, initialPhotos]);

    const loadMore = useCallback(
        async (retryCount = 0) => {
            // Prevent multiple simultaneous requests
            if (isLoadingMore || noMorePhotos) return;

            // Cancel any existing request
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }

            // Clear any existing retry timeout
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
            }

            setIsLoadingMore(true);
            setError(null);

            // Create new AbortController for this request
            abortControllerRef.current = new AbortController();

            const maxRetries = 3;
            const baseDelay = 1000; // 1 second

            try {
                const response = await fetch(
                    `/api/more-photos?placeId=${placeId}&page=${page + 1}&limit=20`,
                    {
                        signal: abortControllerRef.current.signal,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data: PhotosApiResponse = await response.json();

                // Validate response structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid response format');
                }

                if (data.error) {
                    throw new Error(data.error);
                }

                if (data.photos && Array.isArray(data.photos)) {
                    if (data.photos.length > 0) {
                        // Merge new photos with existing ones
                        setPhotos((prevPhotos) => [...prevPhotos, ...data.photos!]);
                        setPage(page + 1);

                        // Check if there are more photos available
                        if (data.hasMore === false) {
                            setNoMorePhotos(true);
                        }
                    } else {
                        setNoMorePhotos(true);
                    }
                } else {
                    throw new Error('Invalid photos data structure');
                }
            } catch (error: any) {
                // Check if request was aborted
                if (error instanceof Error && error.message.includes('aborted')) {
                    // Request was cancelled, don't show error
                    return;
                }

                console.error('Error loading more photos:', error);

                // Implement retry with exponential backoff for network errors
                if (
                    retryCount < maxRetries &&
                    (error.message?.includes('fetch') ||
                        error.message?.includes('network') ||
                        error.message?.includes('HTTP error'))
                ) {
                    const delay = baseDelay * Math.pow(2, retryCount) + Math.random() * 1000; // Add jitter
                    console.log(
                        `Retrying photo load in ${Math.round(delay)}ms... (attempt ${retryCount + 1
                        }/${maxRetries})`
                    );

                    retryTimeoutRef.current = setTimeout(() => {
                        loadMore(retryCount + 1);
                    }, delay);
                    return; // Don't remove loading state yet
                } else {
                    // After exhausting all retries, show error to user
                    setError(
                        error.message ||
                        'Error loading more photos after multiple attempts. Please try again.'
                    );
                }
            } finally {
                // Only remove loading if not retrying
                if (retryCount >= maxRetries || error) {
                    setIsLoadingMore(false);
                    abortControllerRef.current = null;
                }
            }
        },
        [isLoadingMore, noMorePhotos, placeId, page, error]
    );

    const resetError = useCallback(() => {
        setError(null);
    }, []);

    // Cleanup effect
    useEffect(() => {
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
            }
        };
    }, []);

    return {
        photos,
        isLoadingMore,
        error,
        noMorePhotos,
        loadMore: () => loadMore(0),
        resetError,
        currentCount: photos.length,
        totalCount: totalPhotosAvailable,
    };
}