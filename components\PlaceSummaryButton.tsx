import React, { memo } from "react";
import { PlaceResult, Review } from "../types/place";

interface PlaceSummaryButtonProps {
  place: PlaceResult;
  reviews: Review[];
  onCopy: (text: string, field: string) => void;
}

/**
 * Button component that formats and copies place information summary
 */
const PlaceSummaryButton: React.FC<PlaceSummaryButtonProps> = memo(
  ({ place, reviews, onCopy }) => {
    const formatPlaceSummary = () => {
      // Get top 5 reviews
      const topReviews = reviews.slice(0, 5);

      // Format the summary
      let summary = "";

      // URL
      summary += `URL SITE: ${place.website || "N/A"} | \n`;

      // NAME (Place name)
      summary += `NAME: ${place.name} | \n`;

      // Location
      summary += `LOCALIZAÇÃO: ${place.formatted_address} | \n`;

      // Google Maps URL
      summary += `LOCALIZAÇÃO URL: ${place.url || "N/A"} | \n`;

      // Phone
      summary += `TELEFONE: ${place.formatted_phone_number || "N/A"} | \n`;

      // International Phone with WhatsApp link
      if (place.international_phone_number) {
        // Remove all non-numeric characters for WhatsApp link
        const cleanPhone = place.international_phone_number.replace(/\D/g, "");
        const whatsappLink = `https://wa.me/${cleanPhone}`;
        summary += `WHATSAPP: ${whatsappLink} | \n`;
      } else {
        summary += `WHATSAPP: N/A | \n`;
      }

      // Reviews with author name
      topReviews.forEach((review, index) => {
        const reviewText = review.text.replace(/\n/g, " ").trim();
        const authorName = review.author_name;
        summary += `REVIEW ${index + 1}: ${authorName} - ${reviewText} | \n`;
      });

      // Fill empty review slots if less than 5 reviews
      for (let i = topReviews.length; i < 5; i++) {
        summary += `REVIEW ${i + 1}: | \n`;
      }

      return summary.trim();
    };

    const handleCopyClick = () => {
      const summary = formatPlaceSummary();
      onCopy(summary, "Place Summary");
    };

    return (
      <button
        onClick={handleCopyClick}
        className="btn-primary w-full text-center inline-flex items-center justify-center mb-3"
        type="button"
        aria-label="Copy place summary information"
      >
        <svg
          className="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
          />
        </svg>
        Copy Summary
      </button>
    );
  }
);

PlaceSummaryButton.displayName = "PlaceSummaryButton";

export default PlaceSummaryButton;
