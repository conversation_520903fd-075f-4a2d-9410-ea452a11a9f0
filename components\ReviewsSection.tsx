import React, { memo } from "react";
import { Review } from "../types/place";
import ReviewItem from "./ReviewItem";
import LoadingButton from "./LoadingButton";
import ErrorMessage from "./ErrorMessage";
import LoadingSkeleton from "./LoadingSkeleton";

interface ReviewsSectionProps {
  reviews: Review[];
  isLoadingMore: boolean;
  error: string | null;
  noMoreReviews: boolean;
  onLoadMore: () => void;
  onResetError: () => void;
  onCopy: (text: string, field: string) => void;
}

/**
 * Reviews section component with pagination and error handling
 */
const ReviewsSection: React.FC<ReviewsSectionProps> = memo(
  ({
    reviews,
    isLoadingMore,
    error,
    noMoreReviews,
    onLoadMore,
    onResetError,
    onCopy,
  }) => {
    if (!reviews || reviews.length === 0) {
      return (
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Reviews</h2>
          <div className="text-center text-gray-500 py-8">
            <svg
              className="mx-auto h-12 w-12 text-gray-400 mb-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
            <p>No reviews available for this place yet.</p>
          </div>
        </div>
      );
    }

    return (
      <section
        className="mt-8 bg-white rounded-lg shadow-md p-6"
        aria-labelledby="reviews-heading"
      >
        <h2
          id="reviews-heading"
          className="text-xl font-bold text-gray-900 mb-4"
        >
          Reviews
          <span className="text-sm font-normal text-gray-500 ml-2">
            ({reviews.length} {reviews.length === 1 ? "review" : "reviews"})
          </span>
        </h2>

        <div className="space-y-6" role="list">
          {reviews.map((review, index) => (
            <div
              key={`${review.author_name}-${review.time}-${index}`}
              role="listitem"
            >
              <ReviewItem review={review} onCopy={onCopy} />
            </div>
          ))}
        </div>

        <div className="mt-8 flex flex-col items-center space-y-4">
          {/* Error message */}
          {error && (
            <ErrorMessage
              error={error}
              onRetry={() => {
                onResetError();
                onLoadMore();
              }}
              className="w-full max-w-md"
            />
          )}

          {/* Load more button */}
          {!noMoreReviews && !error && (
            <LoadingButton
              onClick={onLoadMore}
              isLoading={isLoadingMore}
              loadingText="Loading reviews..."
              variant="primary"
              size="md"
              ariaLabel="Load more reviews"
            >
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Load more reviews
              </span>
            </LoadingButton>
          )}

          {/* No more reviews message */}
          {noMoreReviews && !error && (
            <div className="text-center">
              <svg
                className="mx-auto h-8 w-8 text-gray-400 mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="text-gray-500 text-sm">
                All reviews have been loaded.
              </p>
            </div>
          )}

          {/* Loading skeleton */}
          {isLoadingMore && (
            <div className="w-full">
              <LoadingSkeleton type="review" count={3} />
            </div>
          )}
        </div>
      </section>
    );
  }
);

ReviewsSection.displayName = "ReviewsSection";

export default ReviewsSection;
