/* Animation styles for components */

@keyframes slide-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fade-out {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

.animate-slide-in-right {
    animation: slide-in-right 0.3s ease-out;
}

.animate-fade-out {
    animation: fade-out 0.3s ease-out forwards;
}

/* Skeleton loading animation */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

.animate-pulse {
    background: linear-gradient(90deg,
            #f3f4f6 25%,
            #e5e7eb 50%,
            #f3f4f6 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Aspect ratio utilities for responsive images */
.aspect-w-4 {
    position: relative;
    padding-bottom: 75%;
    /* 4:3 Aspect Ratio */
}

.aspect-w-16 {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 Aspect Ratio */
}

.aspect-h-3,
.aspect-h-9 {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
}