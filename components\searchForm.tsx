import React, { useRef, useState } from "react";
import { useRouter } from "next/router";
import { useLoadScript, Autocomplete } from "@react-google-maps/api";

const scriptOptions = {
  googleMapsApiKey: process.env.NEXT_PUBLIC_API_KEY as string,
  libraries: ["places"] as any,
};

export default function SearchForm() {
  const router = useRouter();
  const { isLoaded, loadError } = useLoadScript(scriptOptions);
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const inputEl = useRef<HTMLInputElement | null>(null);

  // Handle the keypress for input
  const onKeypress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // On enter pressed
    if (e.key === "Enter") {
      e.preventDefault();
      return false;
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const onLoad = (autocompleteObj: google.maps.places.Autocomplete) => {
    setAutocomplete(autocompleteObj);
  };

  const onPlaceChanged = () => {
    if (autocomplete) {
      const place = autocomplete.getPlace();
      if ("place_id" in place) {
        setIsLoading(true);
        router.push(`/place/${place.place_id}`);
      }
    }
  };

  if (loadError) {
    return (
      <div className="card p-8 text-center">
        <div className="text-red-600 mb-4">
          <svg
            className="mx-auto h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Failed to load
        </h3>
        <p className="text-gray-600 mb-4">
          Não foi possível carregar o script do Google Maps. Por favor,
          recarregue a página.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="btn-primary"
        >
          Recarregar Página
        </button>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="card p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    );
  }

  return (
    <div className="card p-8 rounded-3xl">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <Autocomplete
            onLoad={onLoad}
            fields={["place_id", "name", "formatted_address"]}
            onPlaceChanged={onPlaceChanged}
          >
            <input
              ref={inputEl}
              type="text"
              className="form-input text-lg py-4 pl-12 pr-4 bg-gray-100 rounded-3xl"
              placeholder="Place, city, or address..."
              onKeyPress={onKeypress}
              disabled={isLoading}
            />
          </Autocomplete>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          {isLoading && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
            </div>
          )}
        </div>

        <div className="text-center text-sm text-gray-500">
          <p>Select a place from the suggestions list to see details</p>
        </div>
      </form>
    </div>
  );
}
