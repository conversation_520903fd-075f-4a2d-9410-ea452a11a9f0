import React, { memo } from "react";

interface LoadingButtonProps {
  onClick: () => void;
  isLoading: boolean;
  disabled?: boolean;
  loadingText?: string;
  children: React.ReactNode;
  variant?: "primary" | "secondary";
  size?: "sm" | "md" | "lg";
  className?: string;
  ariaLabel?: string;
}

const variantClasses = {
  primary: {
    base: "border-indigo-600 text-indigo-600 hover:text-white hover:bg-indigo-600",
    loading: "bg-gray-100 text-gray-500 border-gray-300",
  },
  secondary: {
    base: "border-gray-300 text-gray-700 hover:bg-gray-50",
    loading: "bg-gray-100 text-gray-500 border-gray-300",
  },
};

const sizeClasses = {
  sm: "px-3 py-1.5 text-xs",
  md: "px-4 py-2 text-sm",
  lg: "px-6 py-3 text-base",
};

/**
 * Reusable loading button component with consistent styling
 */
const LoadingButton: React.FC<LoadingButtonProps> = memo(
  ({
    onClick,
    isLoading,
    disabled = false,
    loadingText,
    children,
    variant = "primary",
    size = "md",
    className = "",
    ariaLabel,
  }) => {
    const variantClass = isLoading
      ? variantClasses[variant].loading
      : variantClasses[variant].base;
    const sizeClass = sizeClasses[size];
    const isDisabled = disabled || isLoading;

    return (
      <button
        onClick={onClick}
        disabled={isDisabled}
        className={`
        rounded-md border font-medium transition-all duration-200
        ${variantClass}
        ${sizeClass}
        ${
          isDisabled
            ? "cursor-not-allowed"
            : "hover:shadow-md transform hover:scale-105"
        }
        focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
        ${className}
      `}
        aria-label={ariaLabel}
        aria-busy={isLoading}
        type="button"
      >
        {isLoading ? (
          <span className="flex items-center justify-center">
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            {loadingText || "Loading..."}
          </span>
        ) : (
          children
        )}
      </button>
    );
  }
);

LoadingButton.displayName = "LoadingButton";

export default LoadingButton;
