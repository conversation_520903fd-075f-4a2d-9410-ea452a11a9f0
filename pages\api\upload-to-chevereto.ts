/**
 * API endpoint para upload de imagens para Chevereto
 *
 * Este endpoint recebe uma URL de imagem ou múltiplas URLs e faz upload para o serviço Chevereto
 * configurado, retornando a URL da imagem hospedada ou um array de resultados para batch uploads.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import FormData from 'form-data';
import https from 'https';
import { URL } from 'url';

// Types for batch upload responses
interface UploadResult {
  originalUrl: string;
  uploadedUrl: string;
  viewerUrl: string;
  filename: string;
  size: string;
  uploadDate: string;
}

interface UploadError {
  originalUrl: string;
  error: string;
  message: string;
}

interface BatchUploadResponse {
  success: boolean;
  message: string;
  data: {
    successful: UploadResult[];
    failed: UploadError[];
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  };
}

interface SingleUploadResponse {
  success: boolean;
  message: string;
  data: {
    url: string;
    viewerUrl: string;
    filename: string;
    size: string;
    uploadDate: string;
  };
}

/**
 * Upload a single image to Chevereto
 */
async function uploadSingleImage(imageUrl: string): Promise<UploadResult> {
  // Validar se é uma URL válida
  try {
    new URL(imageUrl);
  } catch (error) {
    throw new Error('A URL fornecida não é válida');
  }

  // Validar se é uma URL de imagem do Google (por segurança)
  if (!imageUrl.includes('googleapis.com')) {
    throw new Error('Apenas URLs de imagens do Google Places são permitidas');
  }

  // Primeiro, fazer download da imagem
  console.log('Fazendo download da imagem:', imageUrl);
  const imageResponse = await fetch(imageUrl);

  if (!imageResponse.ok) {
    console.error('Erro ao fazer download da imagem:', imageResponse.status);
    throw new Error('Não foi possível fazer download da imagem do Google Places');
  }

  // Obter o buffer da imagem
  const imageBuffer = await imageResponse.arrayBuffer();
  const buffer = Buffer.from(imageBuffer);

  // Determinar extensão baseada no content-type
  const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';
  const extension = contentType.includes('png') ? 'png' : 'jpg';
  const filename = `gplaces-img.${extension}`;

  // Preparar dados para o Chevereto usando form-data
  const formData = new FormData();
  formData.append('source', buffer, {
    filename: filename,
    contentType: contentType
  });
  formData.append('format', 'json');

  console.log('Enviando para Chevereto, tamanho:', buffer.length, 'bytes');

  // Fazer requisição para a API do Chevereto usando https module
  const cheveretoResponse = await new Promise<{
    ok: boolean;
    status: number;
    text: () => Promise<string>;
    json: () => Promise<any>;
  }>((resolve, reject) => {
    const url = new URL('https://db.avenca.cloud/api/1/upload');

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'X-API-Key': process.env.CHEVERETO_API,
        ...formData.getHeaders(),
      },
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          ok: (res.statusCode ?? 500) >= 200 && (res.statusCode ?? 500) < 300,
          status: res.statusCode ?? 500,
          text: async () => data,
          json: async () => JSON.parse(data),
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    formData.pipe(req);
  });

  // Verificar se a resposta foi bem-sucedida
  if (!cheveretoResponse.ok) {
    const errorText = await cheveretoResponse.text();
    console.error('Erro na API do Chevereto:', cheveretoResponse.status, errorText);

    // Tratar diferentes tipos de erro HTTP
    switch (cheveretoResponse.status) {
      case 401:
        throw new Error('Chave da API inválida ou expirada');
      case 403:
        throw new Error('Sem permissão para fazer upload');
      case 413:
        throw new Error('A imagem excede o tamanho máximo permitido');
      case 415:
        throw new Error('Formato de imagem não é suportado');
      case 429:
        throw new Error('Limite de uploads excedido. Tente novamente em alguns minutos');
      case 503:
        throw new Error('Servidor de upload temporariamente indisponível');
      default:
        throw new Error('Erro interno no serviço de upload');
    }
  }

  // Processar resposta JSON
  const cheveretoData = await cheveretoResponse.json();

  // Verificar se o upload foi bem-sucedido
  if (cheveretoData.status_code !== 200 || !cheveretoData.success) {
    console.error('Erro no upload do Chevereto:', cheveretoData);
    throw new Error(cheveretoData.error?.message || 'Não foi possível fazer upload da imagem');
  }

  // Extrair URL da imagem uploadada
  const uploadedImageUrl = cheveretoData.image?.url;
  if (!uploadedImageUrl) {
    console.error('URL da imagem não encontrada na resposta:', cheveretoData);
    throw new Error('URL da imagem não foi retornada pelo serviço');
  }

  // Forçar HTTPS no URL retornado
  const httpsUrl = uploadedImageUrl.replace(/^http:\/\//i, 'https://');
  const httpsViewerUrl = cheveretoData.image?.url_viewer?.replace(/^http:\/\//i, 'https://');

  return {
    originalUrl: imageUrl,
    uploadedUrl: httpsUrl,
    viewerUrl: httpsViewerUrl,
    filename: cheveretoData.image?.filename,
    size: cheveretoData.image?.size_formatted,
    uploadDate: cheveretoData.image?.date,
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Apenas aceitar requisições POST
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Método não permitido',
      message: 'Este endpoint aceita apenas requisições POST'
    });
  }

  // Validar se a chave da API está configurada
  if (!process.env.CHEVERETO_API) {
    console.error('CHEVERETO_API não está configurada no ambiente');
    return res.status(500).json({
      error: 'Configuração do servidor',
      message: 'Serviço de upload não está configurado corretamente'
    });
  }

  const { imageUrl, imageUrls } = req.body;

  // Determinar se é upload único ou em lote
  const isBatchUpload = Array.isArray(imageUrls) && imageUrls.length > 0;
  const isSingleUpload = typeof imageUrl === 'string' && imageUrl.length > 0;

  if (!isBatchUpload && !isSingleUpload) {
    return res.status(400).json({
      error: 'Parâmetro obrigatório',
      message: 'URL da imagem (imageUrl) ou array de URLs (imageUrls) é obrigatório'
    });
  }

  if (isBatchUpload && isSingleUpload) {
    return res.status(400).json({
      error: 'Parâmetros conflitantes',
      message: 'Forneça apenas imageUrl OU imageUrls, não ambos'
    });
  }

  try {
    if (isBatchUpload) {
      // Processar upload em lote
      console.log(`Iniciando upload em lote de ${imageUrls.length} imagens`);

      const successful: UploadResult[] = [];
      const failed: UploadError[] = [];

      // Processar imagens sequencialmente para evitar sobrecarregar o servidor
      for (let i = 0; i < imageUrls.length; i++) {
        const currentImageUrl = imageUrls[i];
        console.log(`Processando imagem ${i + 1} de ${imageUrls.length}: ${currentImageUrl}`);

        try {
          // Adicionar pequeno delay entre uploads para evitar rate limiting
          if (i > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 segundo de delay
          }

          const result = await uploadSingleImage(currentImageUrl);
          successful.push(result);
          console.log(`Imagem ${i + 1} enviada com sucesso`);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido no upload';
          failed.push({
            originalUrl: currentImageUrl,
            error: 'Upload failed',
            message: errorMessage
          });
          console.error(`Erro no upload da imagem ${i + 1}:`, errorMessage);
        }
      }

      // Retornar resultado do lote
      const response: BatchUploadResponse = {
        success: successful.length > 0,
        message: successful.length === imageUrls.length
          ? 'Todas as imagens foram enviadas com sucesso'
          : failed.length === imageUrls.length
            ? 'Falha no upload de todas as imagens'
            : `${successful.length} de ${imageUrls.length} imagens enviadas com sucesso`,
        data: {
          successful,
          failed,
          summary: {
            total: imageUrls.length,
            successful: successful.length,
            failed: failed.length
          }
        }
      };

      return res.status(200).json(response);
    } else {
      // Processar upload único (manter compatibilidade)
      console.log('Processando upload único:', imageUrl);

      const result = await uploadSingleImage(imageUrl);

      // Retornar resposta no formato original para compatibilidade
      const response: SingleUploadResponse = {
        success: true,
        message: 'Imagem enviada com sucesso',
        data: {
          url: result.uploadedUrl,
          viewerUrl: result.viewerUrl,
          filename: result.filename,
          size: result.size,
          uploadDate: result.uploadDate,
        }
      };

      return res.status(200).json(response);
    }
  } catch (error) {
    console.error('Erro ao fazer upload para Chevereto:', error);

    // Tratar diferentes tipos de erro
    if (error instanceof Error) {
      if ((error as NodeJS.ErrnoException).code === 'ECONNREFUSED') {
        return res.status(503).json({
          error: 'Erro de conectividade',
          message: 'Não foi possível conectar ao serviço de upload. Tente novamente em alguns instantes'
        });
      }

      if ((error as NodeJS.ErrnoException).code === 'ETIMEDOUT') {
        return res.status(408).json({
          error: 'Timeout',
          message: 'Upload demorou muito para completar. Tente novamente'
        });
      }
    }

    return res.status(500).json({
      error: 'Erro interno',
      message: 'Erro inesperado durante o upload. Tente novamente'
    });
  }
}
