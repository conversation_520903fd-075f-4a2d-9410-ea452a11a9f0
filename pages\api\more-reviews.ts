/**
 * API endpoint para carregar mais reviews de um lugar usando Google Places API
 *
 * ESTRATÉGIA MELHORADA:
 * A API do Google Places tem limitações para paginação de reviews:
 * - Cada chamada retorna no máximo 5 reviews
 * - Não há suporte direto para paginação de reviews
 *
 * Esta implementação maximiza dados reais através de:
 * 1. Múltiplas chamadas com diferentes parâmetros de ordenação (most_relevant + newest)
 * 2. Deduplicação inteligente para obter reviews únicos
 * 3. Cache eficiente para evitar chamadas desnecessárias
 * 4. Fallback mínimo para dados simulados apenas quando absolutamente necessário
 */

import { NextApiRequest, NextApiResponse } from 'next';

interface Review {
  author_name: string;
  profile_photo_url?: string;
  rating: number;
  text: string;
  time: number;
  _simulated?: boolean;
  _fallback?: boolean;
}

// Cache para armazenar reviews já carregadas e evitar duplicatas
const reviewsCache = new Map<string, Review[]>();

/**
 * Deduplica reviews baseado em múltiplos critérios
 */
function deduplicateReviews(reviews: Review[]): Review[] {
  const seen = new Set();
  const unique = [];

  for (const review of reviews) {
    // Criar chave única baseada em autor, texto e tempo
    const key = `${review.author_name}_${review.text?.substring(0, 50)}_${review.time}`;

    if (!seen.has(key)) {
      seen.add(key);
      unique.push(review);
    }
  }

  return unique;
}

/**
 * Busca reviews da API do Google Places com parâmetro de ordenação específico
 */
async function fetchReviewsFromAPI(placeId: string, sortBy: string = 'most_relevant'): Promise<Review[]> {
  const googleApiUrl = `https://maps.googleapis.com/maps/api/place/details/json?key=${process.env.GPLACES_API}&place_id=${placeId}&fields=reviews&reviews_sort=${sortBy}`;

  try {
    const response = await fetch(googleApiUrl);
    const data = await response.json();

    if (data.status === 'OK' && data.result && data.result.reviews) {
      return data.result.reviews;
    }

    return [];
  } catch (error) {
    console.warn(`Erro ao buscar reviews com ordenação ${sortBy}:`, error);
    return [];
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Método não permitido' });
  }

  const { placeId, page = 1, limit = 5 } = req.query;

  // Validate required parameters
  if (!placeId) {
    return res.status(400).json({ error: 'É necessário fornecer um ID de lugar' });
  }

  // Ensure placeId is a string
  const placeIdString = Array.isArray(placeId) ? placeId[0] : placeId;

  // Validate pagination parameters
  const pageNum = parseInt(Array.isArray(page) ? page[0] : String(page));
  const limitNum = parseInt(Array.isArray(limit) ? limit[0] : String(limit));

  if (isNaN(pageNum) || pageNum < 1) {
    return res.status(400).json({ error: 'Número da página deve ser um inteiro positivo' });
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 20) {
    return res.status(400).json({ error: 'Limite deve ser um inteiro entre 1 e 20' });
  }

  try {
    // Chave do cache para este lugar
    const cacheKey = `${placeIdString}_reviews`;
    const allReviewsCacheKey = `${placeIdString}_all_reviews`;

    // Tentar buscar reviews adicionais da API do Google Places
    let newReviews: Review[] = [];
    let hasMore = false;

    // ESTRATÉGIA MELHORADA: Múltiplas chamadas para maximizar dados reais
    if (pageNum === 1) {
      // Para a primeira página adicional, fazer múltiplas chamadas à API
      console.log('Buscando reviews adicionais com múltiplas estratégias...');

      try {
        // Fazer chamadas paralelas com diferentes ordenações
        const [relevantReviews, newestReviews] = await Promise.all([
          fetchReviewsFromAPI(placeIdString, 'most_relevant'),
          fetchReviewsFromAPI(placeIdString, 'newest')
        ]);

        // Combinar e deduplicar reviews
        const allApiReviews = [...relevantReviews, ...newestReviews];
        const uniqueReviews = deduplicateReviews(allApiReviews);

        console.log(`Obtidos ${allApiReviews.length} reviews da API, ${uniqueReviews.length} únicos após deduplicação`);

        // Armazenar no cache
        reviewsCache.set(allReviewsCacheKey, uniqueReviews);

        // Assumir que os primeiros 5 reviews já foram mostrados na página inicial
        // Retornar os próximos reviews únicos
        const additionalReviews = uniqueReviews.slice(5);
        newReviews = additionalReviews.slice(0, limitNum);
        hasMore = additionalReviews.length > limitNum;

        console.log(`Retornando ${newReviews.length} reviews adicionais, hasMore: ${hasMore}`);

      } catch (apiError) {
        console.warn('Erro ao buscar da API do Google:', apiError);
        // Fallback será tratado abaixo
      }
    } else {
      // Para páginas subsequentes, usar cache de reviews únicos
      const cachedUniqueReviews = reviewsCache.get(allReviewsCacheKey) || [];
      const startIndex = 5 + ((pageNum - 1) * limitNum); // 5 da página inicial + offset
      newReviews = cachedUniqueReviews.slice(startIndex, startIndex + limitNum);
      hasMore = cachedUniqueReviews.length > startIndex + limitNum;

      console.log(`Página ${pageNum}: Retornando ${newReviews.length} reviews do cache, hasMore: ${hasMore}`);
    }

    // Fallback para dados simulados APENAS quando não há dados reais suficientes
    // E apenas para a primeira página adicional
    if (newReviews.length === 0 && pageNum === 1) {
      console.log('AVISO: Usando dados simulados como último recurso - API não retornou reviews adicionais');

      // Dados simulados mais conservadores - apenas alguns reviews de exemplo
      const fallbackReviews = [
        {
          author_name: 'Usuário Anônimo',
          profile_photo_url: '',
          rating: 4,
          text: 'Experiência positiva no local. Recomendo a visita.',
          time: Math.floor(Date.now() / 1000) - (30 * 86400), // 30 dias atrás
          _simulated: true,
          _fallback: true
        },
        {
          author_name: 'Visitante',
          profile_photo_url: '',
          rating: 3,
          text: 'Local interessante, vale a pena conhecer.',
          time: Math.floor(Date.now() / 1000) - (45 * 86400), // 45 dias atrás
          _simulated: true,
          _fallback: true
        }
      ];

      newReviews = fallbackReviews.slice(0, Math.min(limitNum, 2)); // Máximo 2 reviews simulados
      hasMore = false; // Não simular mais páginas

      console.log(`Retornando ${newReviews.length} reviews simulados como fallback`);
    }

    // Simular atraso de rede realístico apenas se houver dados
    if (newReviews.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));
    }

    // Determinar fonte dos dados
    const hasSimulated = newReviews.some(r => r._simulated);
    const hasFallback = newReviews.some(r => r._fallback);
    let dataSource = 'google_api';

    if (hasFallback) {
      dataSource = 'fallback_simulated';
    } else if (hasSimulated) {
      dataSource = 'mixed';
    }

    return res.status(200).json({
      reviews: newReviews,
      hasMore: hasMore,
      page: pageNum,
      limit: limitNum,
      source: dataSource,
      total: newReviews.length,
      message: newReviews.length === 0 ? 'Não há mais avaliações disponíveis para este local.' :
        hasFallback ? 'Dados limitados da API. Algumas avaliações são exemplos.' :
          hasSimulated ? 'Combinação de dados reais e simulados da API.' :
            'Dados obtidos diretamente da API do Google Places.'
    });

  } catch (error) {
    console.error('Erro ao buscar mais reviews:', error);

    // Return appropriate error response
    if (error instanceof Error &&
      (error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT'))) {
      return res.status(503).json({ error: 'Erro de conectividade. Tente novamente em alguns instantes.' });
    }

    return res.status(500).json({ error: 'Erro interno do servidor ao buscar avaliações' });
  }
}