/**
 * Type definitions for Google Places API data structures
 */

export interface Photo {
    thumbnail: string;
    large: string;
    attributions: string[];
    width: number;
    height: number;
}

export interface Review {
    author_name: string;
    profile_photo_url?: string;
    rating: number;
    text: string;
    time: number;
}

export interface OpeningHours {
    open_now: boolean;
    weekday_text: string[];
}

export interface PlaceResult {
    name: string;
    formatted_address: string;
    place_id: string;
    rating?: number;
    types: string[];
    reviews?: Review[];
    formatted_phone_number?: string;
    international_phone_number?: string;
    website?: string;
    url?: string;
    price_level?: number;
    opening_hours?: OpeningHours;
    photos?: any[];
}

export interface PlaceDetailsData {
    status: string;
    result: PlaceResult;
    photos: Photo[];
    totalPhotosAvailable: number;
}

export interface PlaceDetailsProps {
    data: PlaceDetailsData;
}

// API Response types
export interface ReviewsApiResponse {
    reviews?: Review[];
    hasMore?: boolean;
    error?: string;
}

export interface PhotosApiResponse {
    photos?: Photo[];
    hasMore?: boolean;
    error?: string;
}

// Utility types
export type PriceLevel = 0 | 1 | 2 | 3 | 4;

export interface PriceLevelMap {
    [key: number]: string;
}