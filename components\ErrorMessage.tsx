import React, { memo } from "react";

interface ErrorMessageProps {
  error: string;
  onRetry?: () => void;
  className?: string;
}

/**
 * Reusable error message component with optional retry functionality
 */
const ErrorMessage: React.FC<ErrorMessageProps> = memo(
  ({ error, onRetry, className = "" }) => {
    return (
      <div
        className={`bg-red-50 border border-red-200 rounded-md p-4 ${className}`}
        role="alert"
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-red-700">{error}</p>
            {onRetry && (
              <button
                onClick={onRetry}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                type="button"
              >
                Try again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }
);

ErrorMessage.displayName = "ErrorMessage";

export default ErrorMessage;
