import React, { memo } from "react";
import { generateStarRating } from "../utils/place-utils";

interface StarRatingProps {
  rating: number;
  showNumeric?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

const sizeClasses = {
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-6 h-6",
};

/**
 * Reusable star rating component with half-star support
 */
const StarRating: React.FC<StarRatingProps> = memo(
  ({ rating, showNumeric = false, size = "md", className = "" }) => {
    if (!rating) return null;

    const stars = generateStarRating(rating);
    const sizeClass = sizeClasses[size];

    return (
      <div
        className={`flex items-center ${className}`}
        role="img"
        aria-label={`Rating: ${rating} out of 5 stars`}
      >
        <div className="flex">
          {stars.map((type, index) => (
            <div key={index} className="relative">
              {type === "full" && (
                <svg
                  className={`${sizeClass} text-yellow-400`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              )}
              {type === "half" && (
                <svg
                  className={`${sizeClass} text-yellow-400`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <defs>
                    <linearGradient
                      id={`half-star-${index}`}
                      x1="0"
                      x2="100%"
                      y1="0"
                      y2="0"
                    >
                      <stop offset="50%" stopColor="currentColor" />
                      <stop offset="50%" stopColor="#e5e7eb" />
                    </linearGradient>
                  </defs>
                  <path
                    fill={`url(#half-star-${index})`}
                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                  />
                </svg>
              )}
              {type === "empty" && (
                <svg
                  className={`${sizeClass} text-gray-300`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              )}
            </div>
          ))}
        </div>
        {showNumeric && (
          <span className="ml-2 text-gray-600 text-sm">
            {rating.toFixed(1)} / 5
          </span>
        )}
      </div>
    );
  }
);

StarRating.displayName = "StarRating";

export default StarRating;
