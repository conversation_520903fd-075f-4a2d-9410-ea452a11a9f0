import React from "react";
import Head from "next/head";
import { PlaceResult } from "../types/place";
import { getPrimaryType, truncateText } from "../utils/place-utils";

interface PlaceSEOProps {
  place: PlaceResult;
  photos: Array<{ large: string }>;
}

/**
 * SEO component with meta tags and structured data for place pages
 */
const PlaceSEO: React.FC<PlaceSEOProps> = ({ place, photos }) => {
  const primaryType = getPrimaryType(place.types);
  const description = `${place.name} - ${primaryType} in ${
    place.formatted_address
  }. ${place.rating ? `Rated ${place.rating}/5 stars.` : ""}`;

  const truncatedDescription = truncateText(description, 160);
  const imageUrl = photos.length > 0 ? photos[0].large : null;

  // Generate structured data for Google Rich Results
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: place.name,
    address: {
      "@type": "PostalAddress",
      streetAddress: place.formatted_address,
    },
    ...(place.rating && {
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: place.rating,
        bestRating: 5,
        worstRating: 1,
        reviewCount: place.reviews?.length || 0,
      },
    }),
    ...(place.formatted_phone_number && {
      telephone: place.formatted_phone_number,
    }),
    ...(place.website && {
      url: place.website,
    }),
    ...(place.opening_hours && {
      openingHoursSpecification: place.opening_hours.weekday_text?.map(
        (hours, index) => {
          const dayMap = [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
          ];
          return {
            "@type": "OpeningHoursSpecification",
            dayOfWeek: dayMap[index === 6 ? 0 : index + 1],
            opens: hours.includes("Closed") ? undefined : "00:00",
            closes: hours.includes("Closed") ? undefined : "23:59",
          };
        }
      ),
    }),
    ...(imageUrl && {
      image: imageUrl,
    }),
    ...(place.price_level !== undefined && {
      priceRange: "$".repeat(place.price_level + 1),
    }),
    ...(place.reviews &&
      place.reviews.length > 0 && {
        review: place.reviews.slice(0, 5).map((review) => ({
          "@type": "Review",
          author: {
            "@type": "Person",
            name: review.author_name,
          },
          reviewRating: {
            "@type": "Rating",
            ratingValue: review.rating,
            bestRating: 5,
            worstRating: 1,
          },
          reviewBody: review.text,
          datePublished: new Date(review.time * 1000).toISOString(),
        })),
      }),
  };

  return (
    <Head>
      {/* Primary Meta Tags */}
      <title>{`${place.name} - ${primaryType} | Your Site Name`}</title>
      <meta name="title" content={`${place.name} - ${primaryType}`} />
      <meta name="description" content={truncatedDescription} />
      <meta name="robots" content="index, follow" />
      <link
        rel="canonical"
        href={`https://yoursite.com/place/${place.place_id}`}
      />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="business.business" />
      <meta
        property="og:url"
        content={`https://yoursite.com/place/${place.place_id}`}
      />
      <meta property="og:title" content={`${place.name} - ${primaryType}`} />
      <meta property="og:description" content={truncatedDescription} />
      {imageUrl && <meta property="og:image" content={imageUrl} />}
      <meta property="og:locale" content="en_US" />
      <meta property="og:site_name" content="Your Site Name" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta
        property="twitter:url"
        content={`https://yoursite.com/place/${place.place_id}`}
      />
      <meta
        property="twitter:title"
        content={`${place.name} - ${primaryType}`}
      />
      <meta property="twitter:description" content={truncatedDescription} />
      {imageUrl && <meta property="twitter:image" content={imageUrl} />}

      {/* Additional Meta Tags */}
      <meta name="geo.placename" content={place.name} />
      <meta name="geo.position" content="latitude;longitude" />
      <meta name="ICBM" content="latitude, longitude" />

      {/* Business specific meta tags */}
      {place.formatted_phone_number && (
        <meta
          name="business:contact_data:phone_number"
          content={place.formatted_phone_number}
        />
      )}
      {place.website && (
        <meta name="business:contact_data:website" content={place.website} />
      )}
      {place.formatted_address && (
        <meta
          name="business:contact_data:street_address"
          content={place.formatted_address}
        />
      )}

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

export default PlaceSEO;
