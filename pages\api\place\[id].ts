import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse
) {
    // Configura CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');

    const { id } = req.query;

    if (!id || typeof id !== "string") {
        return res.status(400).json({ error: "Place ID is required" });
    }

    try {
        const fields = [
            "name", "photos", "rating", "reviews",
            "formatted_address", "website", "opening_hours",
            "formatted_phone_number", "types", "vicinity"
        ].join(",");

        const url = `https://maps.googleapis.com/maps/api/place/details/json?key=${process.env.GPLACES_API}&place_id=${id}&fields=${fields}`;
        const apiRes = await fetch(url);

        if (!apiRes.ok) {
            throw new Error(`Google API request failed with status ${apiRes.status}`);
        }

        const data = await apiRes.json();

        if (data.status !== "OK") {
            return res.status(404).json({
                error: data.error_message || "Place not found",
                status: data.status
            });
        }

        // Transforma os dados para o formato público
        const responseData = {
            id: data.result.place_id,
            name: data.result.name,
            address: data.result.formatted_address,
            rating: data.result.rating,
            phone: data.result.formatted_phone_number,
            website: data.result.website,
            openingHours: data.result.opening_hours,
            types: data.result.types,
            vicinity: data.result.vicinity,
            photos: data.result.photos?.map((photo: any) => ({
                reference: photo.photo_reference,
                width: photo.width,
                height: photo.height,
                attributions: photo.html_attributions
            })),
            reviews: data.result.reviews?.map((review: any) => ({
                author: review.author_name,
                rating: review.rating,
                text: review.text,
                time: review.time
            }))
        };

        res.status(200).json(responseData);
    } catch (error) {
        console.error("API Error:", error);
        res.status(500).json({
            error: "Internal server error",
            details: error instanceof Error ? error.message : String(error)
        });
    }
}