import { useState, useCallback } from 'react';

interface UseClipboardReturn {
    copySuccess: string;
    copyToClipboard: (text: string, field: string) => void;
}

/**
 * Custom hook for managing clipboard operations with feedback
 * Provides copy functionality with temporary success messages
 */
export function useClipboard(): UseClipboardReturn {
    const [copySuccess, setCopySuccess] = useState<string>('');

    const copyToClipboard = useCallback((text: string, field: string) => {
        if (!text) return;

        navigator.clipboard
            .writeText(text)
            .then(() => {
                setCopySuccess(`${field} copied!`);
                setTimeout(() => setCopySuccess(''), 2000);
            })
            .catch((err) => {
                console.error('Error copying text: ', err);
                setCopySuccess('Failed to copy');
                setTimeout(() => setCopySuccess(''), 2000);
            });
    }, []);

    return {
        copySuccess,
        copyToClipboard,
    };
}