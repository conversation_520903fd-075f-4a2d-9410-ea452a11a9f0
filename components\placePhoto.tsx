import React, { useState } from "react";

interface Photo {
  thumbnail: string;
  large: string;
  attributions: string[];
}

interface PlacePhotoProps {
  photo: Photo;
}

interface UploadState {
  isUploading: boolean;
  success: boolean;
  error: string | null;
  uploadedUrl: string | null;
  uploadedData: any | null;
}

export default function PlacePhoto({ photo }: PlacePhotoProps) {
  const [hasError, setHasError] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);

  // Estados para upload do Chevereto
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    success: false,
    error: null,
    uploadedUrl: null,
    uploadedData: null,
  });
  const [uploadUrlCopied, setUploadUrlCopied] = useState(false);

  const handleImageLoad = () => {};

  const handleImageError = () => {
    setHasError(true);
  };

  const copyImageUrl = async () => {
    try {
      await navigator.clipboard.writeText(photo.large);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Erro ao copiar URL:", err);
    }
  };

  const openImageExternal = () => {
    window.open(photo.large, "_blank", "noopener,noreferrer");
  };

  const uploadToChevereto = async () => {
    // Reset estados anteriores
    setUploadState({
      isUploading: true,
      success: false,
      error: null,
      uploadedUrl: null,
      uploadedData: null,
    });
    setUploadUrlCopied(false);

    try {
      const response = await fetch("/api/upload-to-chevereto", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageUrl: photo.large,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Erro no upload");
      }

      // Upload bem-sucedido
      setUploadState({
        isUploading: false,
        success: true,
        error: null,
        uploadedUrl: data.data.url,
        uploadedData: data.data,
      });

      // Auto-reset após 10 segundos
      setTimeout(() => {
        setUploadState((prev) => ({ ...prev, success: false }));
      }, 10000);
    } catch (error) {
      console.error("Erro no upload:", error);
      setUploadState({
        isUploading: false,
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Erro desconhecido no upload",
        uploadedUrl: null,
        uploadedData: null,
      });

      // Auto-reset erro após 5 segundos
      setTimeout(() => {
        setUploadState((prev) => ({ ...prev, error: null }));
      }, 5000);
    }
  };

  const copyUploadedUrl = async () => {
    if (!uploadState.uploadedUrl) return;

    try {
      await navigator.clipboard.writeText(uploadState.uploadedUrl);
      setUploadUrlCopied(true);
      setTimeout(() => setUploadUrlCopied(false), 2000);
    } catch (err) {
      console.error("Erro ao copiar URL:", err);
    }
  };

  return (
    <div className="card overflow-hidden group hover:shadow-lg transition-shadow duration-300">
      <div className="relative">
        {hasError ? (
          <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
            <div className="text-gray-400 text-center">
              <svg
                className="mx-auto h-8 w-8 mb-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <p className="text-xs">Erro ao carregar</p>
            </div>
          </div>
        ) : (
          <div className="w-full h-48">
            <img
              src={photo.thumbnail}
              alt="Place photo"
              className="w-full h-full object-cover"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </div>
        )}
      </div>

      <div className="p-4">
        <a
          href={photo.large}
          className="btn-primary w-full text-center inline-flex items-center justify-center mb-3"
          download
          target="_blank"
          rel="noopener noreferrer"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download Foto
        </a>

        <button
          onClick={copyImageUrl}
          className="btn-secondary w-full text-center inline-flex items-center justify-center mb-3"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
            />
          </svg>
          {copied ? "URL Copiada!" : "Copiar URL"}
        </button>

        <button
          onClick={openImageExternal}
          className="btn-secondary w-full text-center inline-flex items-center justify-center mb-3"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>
          Abrir Externamente
        </button>

        {/* Botão de Upload para Chevereto */}
        <button
          onClick={uploadToChevereto}
          disabled={uploadState.isUploading}
          className={`w-full text-center inline-flex items-center justify-center mb-3 ${
            uploadState.success
              ? "btn-primary"
              : uploadState.error
              ? "bg-red-600 hover:bg-red-700 text-white border-red-600 px-4 py-2 rounded-md font-semibold text-xs uppercase tracking-widest transition ease-in-out duration-150"
              : "btn-secondary"
          } ${uploadState.isUploading ? "opacity-50 cursor-not-allowed" : ""}`}
        >
          {uploadState.isUploading ? (
            <>
              <svg
                className="animate-spin w-4 h-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Enviando...
            </>
          ) : uploadState.success ? (
            <>
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Enviado!
            </>
          ) : uploadState.error ? (
            <>
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              Erro no Upload
            </>
          ) : (
            <>
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              Upload para Chevereto
            </>
          )}
        </button>

        {/* Exibir URL da imagem enviada */}
        {uploadState.success && uploadState.uploadedUrl && (
          <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="text-xs font-semibold text-green-800 mb-2">
              Imagem enviada com sucesso!
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={uploadState.uploadedUrl}
                readOnly
                className="flex-1 text-xs bg-white border border-green-300 rounded-sm px-2 py-1 text-green-700 font-mono"
                onClick={(e: React.MouseEvent<HTMLInputElement>) =>
                  (e.target as HTMLInputElement).select()
                }
              />
              <button
                onClick={copyUploadedUrl}
                className="px-2 py-1 bg-green-600 text-white text-xs rounded-sm hover:bg-green-700 transition-colors"
                title="Copiar URL"
              >
                {uploadUrlCopied ? (
                  <svg
                    className="w-3 h-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-3 h-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                    />
                  </svg>
                )}
              </button>
            </div>
            {uploadState.uploadedData?.size && (
              <div className="text-xs text-green-600 mt-1">
                Tamanho: {uploadState.uploadedData.size}
              </div>
            )}
          </div>
        )}

        {/* Exibir mensagem de erro */}
        {uploadState.error && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="text-xs font-semibold text-red-800 mb-1">
              Erro no upload:
            </div>
            <div className="text-xs text-red-600">{uploadState.error}</div>
          </div>
        )}

        {photo.attributions && photo.attributions.length > 0 && (
          <div className="text-xs text-gray-500 border-t border-gray-100 pt-3">
            <div className="font-semibold mb-1 text-gray-700">Atribuições:</div>
            <div className="space-y-1">
              {photo.attributions.map((item, i) => (
                <div
                  key={i}
                  className="leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: item }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
