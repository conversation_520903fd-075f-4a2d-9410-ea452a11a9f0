import React, { memo } from "react";
import { PlaceResult } from "../types/place";
import {
  formatPriceLevel,
  isPlaceOpen,
  getDirectionsUrl,
} from "../utils/place-utils";
import CopyButton from "./CopyButton";
import StarRating from "./StarRating";

interface PlaceInfoProps {
  place: PlaceResult;
  onCopy: (text: string, field: string) => void;
}

interface InfoItemProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}

const InfoItem: React.FC<InfoItemProps> = ({ icon, label, children }) => (
  <div className="mb-4 flex items-start">
    <div className="shrink-0 mr-3" aria-hidden="true">
      {icon}
    </div>
    <div className="flex-1">
      <span className="font-medium text-gray-700">{label}:</span>
      <div className="mt-1">{children}</div>
    </div>
  </div>
);

/**
 * Place information component displaying detailed place data
 */
const PlaceInfo: React.FC<PlaceInfoProps> = memo(({ place, onCopy }) => {
  const openStatus = isPlaceOpen(place.opening_hours);
  const directionsUrl = getDirectionsUrl(place.place_id, place.name);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        Detailed Information
      </h2>

      {/* Phone Numbers */}
      {place.formatted_phone_number && (
        <InfoItem
          icon={
            <svg
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
          }
          label="Phone"
        >
          <div className="flex items-center">
            <a
              href={`tel:${place.formatted_phone_number}`}
              className="text-gray-600 hover:text-gray-800"
            >
              {place.formatted_phone_number}
            </a>
            <CopyButton
              text={place.formatted_phone_number}
              field="Phone"
              onCopy={onCopy}
            />
          </div>
        </InfoItem>
      )}

      {place.international_phone_number &&
        place.international_phone_number !== place.formatted_phone_number && (
          <InfoItem
            icon={
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
            }
            label="International Phone"
          >
            <div className="flex items-center">
              <a
                href={`tel:${place.international_phone_number}`}
                className="text-gray-600 hover:text-gray-800"
              >
                {place.international_phone_number}
              </a>
              <CopyButton
                text={place.international_phone_number}
                field="International Phone"
                onCopy={onCopy}
              />
            </div>
          </InfoItem>
        )}

      {/* Website */}
      {place.website && (
        <InfoItem
          icon={
            <svg
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
              />
            </svg>
          }
          label="Website"
        >
          <div className="flex items-center">
            <a
              href={place.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-indigo-600 hover:text-indigo-800 truncate max-w-xs"
            >
              {place.website}
            </a>
            <CopyButton text={place.website} field="Website" onCopy={onCopy} />
          </div>
        </InfoItem>
      )}

      {/* Google Maps */}
      {place.url && (
        <InfoItem
          icon={
            <svg
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          }
          label="Google Maps"
        >
          <div className="flex items-center flex-wrap gap-2">
            <a
              href={place.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-indigo-600 hover:text-indigo-800"
            >
              View on Google Maps
            </a>
            <a
              href={directionsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-indigo-600 hover:text-indigo-800"
            >
              Get Directions
            </a>
            <CopyButton
              text={place.url}
              field="Google Maps Link"
              onCopy={onCopy}
            />
          </div>
        </InfoItem>
      )}

      {/* Rating */}
      {place.rating && (
        <InfoItem
          icon={
            <svg
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
              />
            </svg>
          }
          label="Rating"
        >
          <StarRating rating={place.rating} showNumeric size="md" />
        </InfoItem>
      )}

      {/* Price Level */}
      {place.price_level !== undefined && (
        <InfoItem
          icon={
            <svg
              className="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          }
          label="Price Range"
        >
          <span className="text-gray-600">
            {formatPriceLevel(place.price_level)}
          </span>
        </InfoItem>
      )}

      {/* Opening Hours */}
      {place.opening_hours && (
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <div className="shrink-0 mr-3">
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <span className="font-medium text-gray-700">Opening Hours:</span>
            {openStatus !== null && (
              <span
                className={`ml-2 px-2 py-1 text-xs rounded-full ${
                  openStatus
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {openStatus ? "Open now" : "Closed now"}
              </span>
            )}
          </div>

          {place.opening_hours.weekday_text && (
            <div className="ml-8 mt-2">
              <div className="bg-gray-50 rounded-md p-3">
                {place.opening_hours.weekday_text.map((day, index) => {
                  const today = new Date().getDay();
                  const dayIndex = index === 6 ? 0 : index + 1; // Adjust for Sunday
                  const isToday = dayIndex === today;

                  return (
                    <div
                      key={index}
                      className={`flex justify-between text-sm py-1 ${
                        isToday
                          ? "font-semibold text-gray-900"
                          : "text-gray-600"
                      }`}
                    >
                      <span>{day}</span>
                      <CopyButton
                        text={day}
                        field={`Hours ${index + 1}`}
                        onCopy={onCopy}
                      />
                    </div>
                  );
                })}
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <CopyButton
                    text={place.opening_hours.weekday_text.join("\n")}
                    field="All hours"
                    onCopy={onCopy}
                    className="w-full justify-center"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

PlaceInfo.displayName = "PlaceInfo";

export default PlaceInfo;
